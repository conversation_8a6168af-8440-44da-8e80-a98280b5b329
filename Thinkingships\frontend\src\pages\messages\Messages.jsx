import { useState } from 'react';

const Messages = () => {
  const [selectedChat, setSelectedChat] = useState(null);

  const conversations = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isOnline: false
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
      isOnline: false
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
      isOnline: false
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      isOnline: false
    },
    {
      id: 5,
      name: '<PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      isOnline: false
    },
    {
      id: 6,
      name: 'Grégoire Garnier',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/46.jpg',
      isOnline: false
    },
    {
      id: 7,
      name: 'Étienne DeGrasse',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
      isOnline: false
    }
  ];

  const currentChat = {
    id: 1,
    name: 'Guy Hawkins',
    status: 'Online - Last seen, 2:02pm',
    avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    messages: [
      {
        id: 1,
        text: 'Hey',
        time: '10:15 pm',
        sender: 'other',
        avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
      },
      {
        id: 2,
        text: '😊 I\'ve been dealing with imposter syndrome at work, and it\'s impacting my confidence. Have you ever experienced this? How did you overcome it? 🤔',
        time: '12:15 pm',
        sender: 'me',
        avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
      },
      {
        id: 3,
        text: '💡 I find it difficult to speak up in group discussions or meetings. Any suggestions on how to improve my communication skills and become more assertive? 🗣️😊',
        time: '12:15 pm',
        sender: 'me',
        avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
      },
      {
        id: 4,
        text: '🏢 I\'m considering a job offer, but I\'m torn between the opportunity for growth and the fear of leaving my current zone. 😰 Any thoughts? 🤔💭',
        time: '10:15 pm',
        sender: 'other',
        avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
      }
    ]
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-indigo-400 via-purple-500 to-blue-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/5 to-transparent"></div>
      </div>

      {/* Main Content */}
      <div className="flex w-full h-screen z-10 relative overflow-hidden">
        {/* Left Panel - Conversations List */}
        <div className="w-96 bg-white/95 backdrop-blur-xl border-r border-white/20 flex flex-col shadow-2xl overflow-hidden h-screen">
          {/* Search Bar */}
          <div className="p-5 border-b border-white/20 flex gap-3 items-center bg-gradient-to-r from-white/10 to-white/5">
            <div className="flex-1 relative flex items-center">
              <svg className="absolute left-3 text-gray-500 z-10" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                placeholder="Search"
                className="w-full pl-10 pr-4 py-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-lg text-sm text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
              />
            </div>
            <button className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-lg text-gray-600 hover:text-blue-500 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 5v14M5 12h14"></path>
              </svg>
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {conversations.map((conversation, index) => (
              <div
                key={conversation.id}
                className={`flex items-center p-4 cursor-pointer border-b border-white/10 transition-all duration-300 hover:bg-white/10 relative overflow-hidden group ${
                  selectedChat === conversation.id ? 'bg-blue-50/20 border-l-4 border-l-blue-500' : ''
                }`}
                onClick={() => setSelectedChat(conversation.id)}
              >
                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <img
                  src={conversation.avatar}
                  alt={conversation.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-lg mr-3 relative z-10"
                />
                <div className="flex-1 min-w-0 relative z-10">
                  <div className="font-semibold text-gray-800 text-sm mb-1 truncate">{conversation.name}</div>
                  <div className="text-xs text-gray-600 truncate">{conversation.lastMessage}</div>
                </div>
                <div className="text-xs text-gray-500 font-medium relative z-10">{conversation.time}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Chat Area */}
        <div className="flex-1 flex flex-col bg-white/95 backdrop-blur-xl shadow-2xl h-screen relative">
          <div className="max-w-4xl mx-auto w-full h-full flex flex-col">
            {/* Chat Header */}
            <div className="p-5 border-b border-white/20 flex items-center justify-between bg-gradient-to-r from-white/10 to-white/5">
              <div className="flex items-center">
                <img
                  src={currentChat.avatar}
                  alt={currentChat.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-lg mr-3"
                />
                <div className="flex flex-col">
                  <div className="font-semibold text-gray-800 text-base">{currentChat.name}</div>
                  <div className="text-sm text-green-600 font-medium">{currentChat.status}</div>
                </div>
              </div>
              <div className="flex gap-2">
                <button className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl text-gray-600 hover:text-blue-500 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                </button>
                <button className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl text-gray-600 hover:text-blue-500 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="23 7 16 12 23 17 23 7"></polygon>
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                  </svg>
                </button>
                <button className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl text-gray-600 hover:text-blue-500 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </button>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 p-6 flex flex-col gap-4 overflow-y-auto pb-24">
              {currentChat.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex items-start gap-3 ${
                    message.sender === 'me' ? 'flex-row-reverse' : ''
                  }`}
                >
                  <img
                    src={message.avatar}
                    alt={message.sender === 'me' ? 'You' : 'User'}
                    className="w-10 h-10 rounded-full object-cover flex-shrink-0"
                  />
                  <div className={`flex flex-col gap-1 max-w-[60%] ${message.sender === 'me' ? 'items-end' : 'items-start'}`}>
                    <div
                      className={`px-4 py-3 rounded-2xl text-sm leading-relaxed break-words ${
                        message.sender === 'me'
                          ? 'bg-[#4A99F8] text-white'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {message.text}
                    </div>
                    <div className="text-xs text-gray-500 px-2">
                      {message.time}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Message Input */}
          <div className="absolute bottom-0 left-0 right-0 p-5 border-t border-white/20 flex gap-3 items-end bg-white/95 backdrop-blur-xl">
            <div className="max-w-4xl mx-auto w-full flex gap-3 items-end">
              <div className="flex-1 relative flex items-center bg-white/90 backdrop-blur-sm rounded-3xl px-4 py-2 shadow-lg border border-white/30 focus-within:shadow-xl focus-within:border-blue-500/50 transition-all duration-300">
              <input
                type="text"
                placeholder="Type your message here..."
                className="flex-1 bg-transparent border-none outline-none py-2 text-sm text-gray-700 placeholder-gray-500"
              />
              <button className="w-8 h-8 bg-white/80 backdrop-blur-sm text-gray-600 hover:text-blue-500 rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-md hover:shadow-lg transform hover:scale-105">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                </svg>
              </button>
              <button className="w-8 h-8 bg-white/80 backdrop-blur-sm text-gray-600 hover:text-blue-500 rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-md hover:shadow-lg transform hover:scale-105">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                  <line x1="9" y1="9" x2="9.01" y2="9"></line>
                  <line x1="15" y1="9" x2="15.01" y2="9"></line>
                </svg>
              </button>
              <button className="w-8 h-8 bg-[#4A99F8] hover:bg-blue-600 text-white rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-md hover:shadow-lg transform hover:scale-105">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"></path>
                </svg>
              </button>
            </div>
            <button className="w-12 h-12 bg-[#4A99F8] hover:bg-blue-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
            </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Messages;
